# Copyright (c) 2015 Shiye Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2025/6/16
import time
import logging
from pyspark.sql.functions import expr, row_number, count
from pyspark.sql.window import Window
from base.task_base import TaskBase
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class FeatureFilter(TaskBase):
    """
    过滤源表有值的特征
    """
    def __init__(self):
        super(FeatureFilter, self).__init__()
        self.save_table="seeyii_data_valscore_dev.dwd_ms_cn_comp_val_lcal_expl"

    def process(self, *args, **kwargs):
        table_name = "seeyii_data_valscore.dwd_mt_mtc_local_explain_temp"
        source_table_name = "seeyii_data_valscore.dwd_ms_mtc_app_sour_idx_t"  # 源值表

        # 读取数据
        result_df = self.spark.sql(f"SELECT * FROM {table_name}").cache()

        # 读取源值表并unpivot转换为长格式
        t3 = time.time()
        logger.info("开始处理源值表...")

        # 获取所有特征列（排除compcode）
        source_df = self.spark.sql(f"SELECT * FROM {source_table_name} ")
        feature_cols = [col for col in source_df.columns if col not in ["compcode"] + ['has_ever_been_patent_pledge','is_still_patent_pledge','recent_patentpplication_date','is_totaloperatincome_in1yearbove_10million', 'is_part_in_the_formulationd_revision_of_stands', 'prop_of_class_ii_intell_property_rights', 'is_relat_with_universitiesd_resch_inst', 'is_the_top_500_comps_innovd_entre_compe_in3year', 'prop_of_class_i_intell_property_rights']]

        # 使用stack函数将宽表转换为长表
        stack_expr = f"stack({len(feature_cols)}"
        for i, col_name in enumerate(feature_cols):
            stack_expr += f", '{col_name}', {col_name}"
        stack_expr += ") as (feature, source_value)"

        source_long_df = source_df.select(
            "compcode",
            expr(stack_expr)
        ).filter("source_value is not null")  # 只保留非空源值

        logger.info(f"[✓] 源值表转换完成，耗时 {time.time() - t3:.2f} 秒")

        # Join结果表和源值表，替换feature_value
        t4 = time.time()
        joined_df = result_df.join(
            source_long_df,
            (result_df.compcode == source_long_df.compcode) &
            (result_df.feature == source_long_df.feature),
            "inner"  # inner join只保留有源值的记录
        ).select(
            result_df.compcode,
            result_df.feature,
            source_long_df.source_value.alias("feature_value"),  # 使用源值
            result_df.score_contribution,
            result_df.baseline_score,
            result_df.modified_score,
            result_df.score_contribution_shifted,
            result_df.contribution_ratio,
        ).cache()

        logger.info(f"[✓] Join操作完成，耗时 {time.time() - t4:.2f} 秒")

        # 根据contribution_ratio每家公司取top10
        t6 = time.time()

        # 创建窗口函数，按compcode分区，按contribution_ratio降序排序
        # window_spec = Window.partitionBy("compcode").orderBy(joined_df.contribution_ratio.desc())

        # 添加行号并筛选每个公司的top10
        # ranked_df = joined_df.withColumn("rank", row_number().over(window_spec))
        # MD5(CONCAT_WS(",", compcode, feature)) AS fingerId
        # final_df = ranked_df.filter(ranked_df.rank <= 10).drop("rank").withColumn("fingerid", expr("md5(concat_ws(',', compcode, feature))"))

        # logger.info(f"[✓] 筛选出每家公司top10，耗时 {time.time() - t6:.2f} 秒")

        # 不过滤top10
        final_df = joined_df.withColumn("fingerid", expr("md5(concat_ws(',', compcode, feature))"))

        # # 写入Hive表
        # t5 = time.time()
        self.get_change_data(final_df)
        # logger.info(f"[✓] Hive 表写入完成，耗时 {time.time() - t5:.2f} 秒")

        # 二级指标统计
        self.layer_static(joined_df)

    def layer_static(self, df):
        from pyspark.sql.functions import sum as spark_sum, col, lit, when, isnan, isnull
        """
        分层统计

        :param df:
        :return:
        """
        cfg = self.from_config()
        """
        cfg: {
        "科创能力":{
        "基础背景":["经营年限","注册资本"],
        "创新资质":["是否是高新技术企业"]
            }
        }
        按公司二级指标统计
        """
        # 创建特征到二级指标的映射
        feature_to_secondary = {}
        for primary_category, secondary_categories in cfg.items():
            for secondary_category, features in secondary_categories.items():
                for feature in features:
                    feature_to_secondary[feature] = secondary_category

        # 创建特征到二级指标的映射表达式
        mapping_expr = lit("其他")  # 默认值为"其他"
        for feature, secondary in feature_to_secondary.items():
            mapping_expr = when(col("feature") == feature, lit(secondary)).otherwise(mapping_expr)

        # 为DataFrame添加二级指标列
        df_with_secondary = df.withColumn("secondary_indicator", mapping_expr)

        # 按公司和二级指标聚合分数
        # 处理空值和非数值的score_contribution
        df_clean = df_with_secondary.withColumn(
            "contribution_ratio_clean",
            when(
                (col("contribution_ratio").isNull()) |
                (col("contribution_ratio") == "") |
                (isnan(col("contribution_ratio"))),
                lit(0.0)
            ).otherwise(col("contribution_ratio").cast("double"))
        )

        # 按公司和二级指标分组聚合
        aggregated_df = df_clean.groupBy("compcode", "secondary_indicator").agg(
            spark_sum("contribution_ratio_clean").alias("contribution_ratio_clean"),
            spark_sum(when(col("contribution_ratio_clean") > 0, col("contribution_ratio_clean")).otherwise(0)).alias(
                "positive_contribution"),
            spark_sum(when(col("contribution_ratio_clean") < 0, col("contribution_ratio_clean")).otherwise(0)).alias(
                "negative_contribution"),
            count("feature").alias("feature_count")
        )

        # 添加指纹ID和其他必要字段
        result_df = aggregated_df.withColumn("fingerid", expr("md5(concat_ws(',', compcode, secondary_indicator))")) \
            .withColumn("datastatus", lit(1)) \
            .withColumn("modifytime", expr("date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss')"))

        # 保存
        result_df.write.mode("overwrite").saveAsTable("seeyii_data_valscore_dev.dwd_ms_cn_comp_val_lcal_expl_2")
        # return result_df


    @staticmethod
    def from_config():
        import pkgutil
        import yaml
        data = pkgutil.get_data('dwd.deep_learning.data_explain', 'layer.yaml')
        config = yaml.safe_load(data)
        return config


